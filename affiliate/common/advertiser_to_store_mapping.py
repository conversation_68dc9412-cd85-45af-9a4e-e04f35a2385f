import csv
import os


class AdvertiserToStoreMapping:
    # csv files of advertiser_name (of all networks) to store_name mappings
    # advertiser_name, store_name, network
    def __init__(self, csv_path='../data/all_network_advertiser_mapping.csv'):
        self.csv_path = csv_path
        self.advertiser_to_store_mapping = {}  # Structure: {advertiser_name: store_name}
        self.store_to_network_mapping = {}  # Structure: {store_name: network_name}
        self.ensure_file_exists()
        self.load_mappings()

    def ensure_file_exists(self):
        """Create the CSV file if it doesn't exist"""
        if not os.path.exists(os.path.dirname(self.csv_path)):
            os.makedirs(os.path.dirname(self.csv_path))

        if not os.path.exists(self.csv_path):
            with open(self.csv_path, 'w', newline='') as file:
                writer = csv.writer(file)
                writer.writerow(['advertiser_name', 'store_name', 'network'])

    def load_mappings(self):
        """Load the mappings from CSV into memory"""
        try:
            self.advertiser_to_store_mapping = {}
            self.store_to_network_mapping = {}

            with open(self.csv_path, 'r', newline='') as file:
                reader = csv.DictReader(file)
                for row in reader:
                    if not row['store_name'] or not row['status'] or row['status'] != 'active':
                        continue

                    advertiser_name = row['advertiser_name']
                    store_name = row['store_name']
                    network = row.get('network', 'unknown')  # Default to 'unknown' for backward compatibility
                    status = row['status'] 

                    # Initialize advertiser in mapping if not exists
                    if advertiser_name not in self.advertiser_to_store_mapping:
                        self.advertiser_to_store_mapping[advertiser_name] = {}

                    # Store network-specific mapping
                    self.advertiser_to_store_mapping[advertiser_name]= store_name
                    self.store_to_network_mapping[store_name]= network # each store could be active with only one network
                    

            total_mappings = sum(len(networks) for networks in self.advertiser_to_store_mapping.values())
            print(f"Loaded {total_mappings} advertiser-to-store mappings across {len(self.advertiser_to_store_mapping)} advertisers")
        except Exception as e:
            print(f"Error loading advertiser mapping: {e}")
            self.advertiser_to_store_mapping = {}
            self.store_to_network_mapping = {}

    def get_store_name_for_advertiser(self, advertiser_name):
        """Get the store name for an active advertiser name"""
        return self.advertiser_to_store_mapping.get(advertiser_name)
    
    def get_network_for_store(self, store_name):
        """Get the network for a store name"""
        return self.store_to_network_mapping.get(store_name)


    def get_networks_for_advertiser(self, advertiser_name):
        """Get all networks available for an advertiser"""
        if advertiser_name in self.advertiser_to_store_mapping:
            return list(self.advertiser_to_store_mapping[advertiser_name].keys())
        return []



    def get_advertiser_to_store_mapping(self):
        """Get mapping as a dictionary: {advertiser_name: store_name}"""
        return self.advertiser_to_store_mapping


    def get_store_to_network_mapping(self):
        """Get  mapping as a dictionary: {store_name: network_name}"""
        return self.store_to_network_mapping


def main():
    # Example usage
    mapping = AdvertiserToStoreMapping()

    # # Add some example mappings for different networks
    # mapping.add_or_update_mapping('Amazon', 'Amazon', 'cj')
    # mapping.add_or_update_mapping('Amazon', 'Amazon', 'flexoffers')
    # mapping.add_or_update_mapping('Best Buy', 'Best Buy', 'cj')
    # mapping.add_or_update_mapping('Target', 'Target', 'impact')

    # Get all entries
    advertiser_to_store_mapping = mapping.get_advertiser_to_store_mapping()
    print(f">> Got {len(advertiser_to_store_mapping)} advertisers with network mappings..")
    store_to_network_mapping = mapping.get_store_to_network_mapping()
    print(f">> Got {len(store_to_network_mapping)} stores with networks activated..")

    for advertiser, store in advertiser_to_store_mapping.items():
        print(f"advertiser: {advertiser} -> store: {store} -> on network: {store_to_network_mapping[store]}")


if __name__ == "__main__":
    main()