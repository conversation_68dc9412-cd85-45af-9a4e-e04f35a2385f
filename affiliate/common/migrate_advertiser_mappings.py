#!/usr/bin/env python3
"""
Migration script to convert existing single-network advertiser mapping CSV files
to the new multi-network format.

Usage:
    python migrate_advertiser_mappings.py --input old_file.csv --network cj --output new_file.csv
    
Or to merge multiple files:
    python migrate_advertiser_mappings.py --merge --output merged_file.csv
"""

import argparse
import csv
import os
from pathlib import Path


def migrate_single_file(input_file, network, output_file):
    """Migrate a single CSV file to the new format"""
    print(f"Migrating {input_file} (network: {network}) to {output_file}")
    
    # Read existing mappings from output file if it exists
    existing_mappings = []
    if os.path.exists(output_file):
        with open(output_file, 'r', newline='') as f:
            reader = csv.DictReader(f)
            existing_mappings = list(reader)
    
    # Read input file
    new_mappings = []
    with open(input_file, 'r', newline='') as f:
        reader = csv.DictReader(f)
        for row in reader:
            if row.get('advertiser_name') and row.get('store_name'):
                new_mappings.append({
                    'advertiser_name': row['advertiser_name'],
                    'store_name': row['store_name'],
                    'network': network
                })
    
    # Combine mappings (avoid duplicates)
    all_mappings = existing_mappings.copy()
    for new_mapping in new_mappings:
        # Check if this exact mapping already exists
        exists = any(
            existing['advertiser_name'] == new_mapping['advertiser_name'] and
            existing['network'] == new_mapping['network']
            for existing in all_mappings
        )
        if not exists:
            all_mappings.append(new_mapping)
    
    # Write to output file
    with open(output_file, 'w', newline='') as f:
        fieldnames = ['advertiser_name', 'store_name', 'network']
        writer = csv.DictWriter(f, fieldnames=fieldnames)
        writer.writeheader()
        writer.writerows(all_mappings)
    
    print(f"Successfully migrated {len(new_mappings)} mappings")


def merge_default_files(output_file):
    """Merge default network-specific files into one multi-network file"""
    default_files = [
        ('data/cj_advertiser_mapping.csv', 'cj'),
        ('data/flexoffers_advertiser_mapping.csv', 'flexoffers'),
        ('data/impact_advertiser_mapping.csv', 'impact'),
    ]
    
    all_mappings = []
    
    for file_path, network in default_files:
        if os.path.exists(file_path):
            print(f"Processing {file_path} for network {network}")
            with open(file_path, 'r', newline='') as f:
                reader = csv.DictReader(f)
                for row in reader:
                    if row.get('advertiser_name') and row.get('store_name'):
                        all_mappings.append({
                            'advertiser_name': row['advertiser_name'],
                            'store_name': row['store_name'],
                            'network': network
                        })
        else:
            print(f"File {file_path} not found, skipping...")
    
    if all_mappings:
        # Ensure output directory exists
        os.makedirs(os.path.dirname(output_file), exist_ok=True)
        
        with open(output_file, 'w', newline='') as f:
            fieldnames = ['advertiser_name', 'store_name', 'network']
            writer = csv.DictWriter(f, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(all_mappings)
        
        print(f"Successfully merged {len(all_mappings)} mappings to {output_file}")
    else:
        print("No mappings found to merge")


def main():
    parser = argparse.ArgumentParser(description='Migrate advertiser mapping CSV files to multi-network format')
    parser.add_argument('--input', help='Input CSV file path')
    parser.add_argument('--network', help='Network name for the input file (e.g., cj, flexoffers, impact)')
    parser.add_argument('--output', default='data/multi_network_advertiser_mapping.csv', 
                       help='Output CSV file path (default: data/multi_network_advertiser_mapping.csv)')
    parser.add_argument('--merge', action='store_true', 
                       help='Merge default network files (cj, flexoffers, impact) into output file')
    
    args = parser.parse_args()
    
    if args.merge:
        merge_default_files(args.output)
    elif args.input and args.network:
        migrate_single_file(args.input, args.network, args.output)
    else:
        print("Error: Either use --merge or provide both --input and --network")
        parser.print_help()


if __name__ == "__main__":
    main()
